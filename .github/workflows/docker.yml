name: Build Docker
on:
  push:
    branches: [ main ]
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: docker/setup-buildx-action@v3
      - uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build
        run: docker build -t ${{ secrets.DOCKERHUB_USERNAME }}/cashinblue-next-stack:latest .
      - name: Push
        run: docker push ${{ secrets.DOCKERHUB_USERNAME }}/cashinblue-next-stack:latest
