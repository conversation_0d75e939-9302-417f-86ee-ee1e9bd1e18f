FROM node:20-alpine AS base
WORKDIR /app

# Install dependencies
COPY package.json package-lock.json* yarn.lock* pnpm-lock.yaml* ./
RUN   if [ -f package-lock.json ]; then npm ci;   elif [ -f yarn.lock ]; then yarn install --frozen-lockfile;   elif [ -f pnpm-lock.yaml ]; then corepack enable && pnpm i --frozen-lockfile;   else npm i; fi

# Build
COPY . .
RUN npm run build

# Run
EXPOSE 3000
CMD ["npm", "run", "start"]
