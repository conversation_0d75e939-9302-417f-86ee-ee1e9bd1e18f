# CashinBlue Next Stack (Docker)

React + Next.js (App Router) with SEO, API routes, Supabase logging, GHL integration, and a background worker (cron) for Shorts/blog automation.

## Quick Start
```bash
cp .env.example .env
# Fill in the env vars
docker compose up --build -d
```

- App: http://localhost:3000
- Worker: runs scheduled jobs (YouTube Shorts script → Veo3 render → YouTube upload; weekly blog post)

## Structure
- `app/` Next.js App Router pages and API routes
- `components/` shared UI/helpers
- `lib/` server utilities (Supabase, GHL, OpenAI clients)
- `scripts/worker.ts` node-cron schedules
- `supabase.sql` DB schema for leads, videos, posts, events

## Notes
- Replace Veo3 API endpoint with your real endpoint.
- Connect YouTube OAuth in production runner or use a service account flow.
- If you use WordPress, add your endpoint and credentials (extend lib/wp.ts accordingly).
