import { NextResponse } from "next/server";
import { supabaseService } from "@/lib/supabase";

export async function POST(req: Request) {
  try {
    const ct = req.headers.get("content-type") || "";
    let body: any = {};
    if (ct.includes("application/json")) body = await req.json();
    else {
      const form = await req.formData();
      body = Object.fromEntries(form.entries());
    }
    const { name, email, phone, source = "demo", ...rest } = body;

    if (!supabaseService) {
      return NextResponse.json({ ok:false, error:"Server key missing" }, { status: 500 });
    }
    const { data, error } = await supabaseService.from("leads").insert({
      name, email, phone, source, utm: rest?.utm ? JSON.parse(rest.utm) : null
    }).select("*").single();
    if (error) throw error;

    // TODO: Push to Go High Level contact + start workflow

    return NextResponse.json({ ok:true, lead: data });
  } catch (e:any) {
    return NextResponse.json({ ok:false, error: e.message }, { status: 500 });
  }
}
