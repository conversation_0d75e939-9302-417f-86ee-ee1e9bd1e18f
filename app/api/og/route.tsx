import { ImageResponse } from "next/og";
export const runtime = "edge";

export async function GET(req: Request) {
  const { searchParams } = new URL(req.url);
  const title = searchParams.get("title") || (process.env.SITE_NAME || "CashinBlue");
  return new ImageResponse(
    (
      <div style={{ display:"flex", width:"100%", height:"100%", alignItems:"center", justifyContent:"center", background:'#0b1020', color:'#fff' }}>
        <div style={{ fontSize:72, fontWeight:700, textAlign:'center', padding:'0 5%' }}>{title}</div>
      </div>
    ),
    { width: 1200, height: 630 }
  );
}
