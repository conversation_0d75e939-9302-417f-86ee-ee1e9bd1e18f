import type { Metadata } from "next";
import { notFound } from "next/navigation";
import { getPostBySlug } from "@/lib/posts";

type Props = { params: { slug: string } };

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const post = await getPostBySlug(params.slug);
  if (!post) return {};
  const url = `/blog/${post.slug}`;
  const title = `${post.title}`;
  const desc = post.excerpt ?? "Read more.";
  return {
    title,
    description: desc,
    alternates: { canonical: url },
    openGraph: {
      url,
      title,
      description: desc,
      type: "article",
      publishedTime: post.publishedAt,
      images: [{ url: `/api/og?title=${encodeURIComponent(post.title)}` }],
    },
    twitter: { card: "summary_large_image", title, description: desc },
  };
}

export default async function BlogPost({ params }: Props){
  const post = await getPostBySlug(params.slug);
  if (!post) return notFound();
  return <article dangerouslySetInnerHTML={{ __html: post.html }} />;
}
