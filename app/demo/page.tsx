import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "Book Your Free AI Demo",
  description: "See the Voice AI Agent in action.",
  robots: { index: true, follow: true },
  alternates: { canonical: "/demo" },
};

export default function DemoPage(){
  return (
    <main style={{padding:'4rem 1.5rem', maxWidth:720, margin:'0 auto'}}>
      <h1>Book Your Free AI Demo</h1>
      <p>Fill the form below and our AI will call you to qualify + book your slot.</p>
      <form method="post" action="/api/lead" style={{display:'grid', gap:'0.75rem'}}>
        <input name="name" placeholder="Your name" required />
        <input name="email" type="email" placeholder="Email" required />
        <input name="phone" placeholder="Phone" required />
        <button type="submit">Request Demo</button>
      </form>
      <div style={{marginTop:'2rem'}}>
        {/* Placeholder for web-embed widget */}
        <iframe src="about:blank" title="AI Widget" style={{width:'100%', height:280, border:'1px solid #ddd'}} />
      </div>
    </main>
  )
}
