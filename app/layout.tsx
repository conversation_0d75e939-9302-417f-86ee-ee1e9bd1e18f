import type { Metadata } from "next";

const site = {
  name: process.env.SITE_NAME || "CashinBlue",
  url: process.env.SITE_URL || "https://pro.cashinblue.com",
  description: process.env.SITE_DESC || "24/7 Voice AI Agent that books appointments while you sleep.",
};

export const metadata: Metadata = {
  metadataBase: new URL(site.url),
  title: {
    default: `${site.name} — Voice AI Agent`,
    template: `%s | ${site.name}`,
  },
  description: site.description,
  alternates: { canonical: site.url },
  openGraph: {
    type: "website",
    url: site.url,
    title: site.name,
    description: site.description,
    siteName: site.name,
  },
  twitter: {
    card: "summary_large_image",
    title: site.name,
    description: site.description,
  },
  robots: { index: true, follow: true },
  keywords: ["Voice AI Agent","AI Sales","Appointment Booking","Go High Level","Supabase"],
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body style={{fontFamily:'system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, Helvetica, Arial'}}>
        {children}
      </body>
    </html>
  );
}
