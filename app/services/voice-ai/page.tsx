import { JsonLd } from "@/components/JsonLd";

export const metadata = {
  title: "Voice AI Agent — Inbound, Outbound & Web Widget",
  description: "Your 24/7 sales agent that books appointments while you sleep.",
  alternates: { canonical: "/services/voice-ai" },
};

export default function Page(){
  const ld = {
    "@context":"https://schema.org",
    "@type":"Product",
    name:"Voice AI Agent",
    description:"Inbound, Outbound & Web Widget.",
    brand:{ "@type":"Brand", name: process.env.SITE_NAME || "CashinBlue" },
    offers:{ "@type":"Offer", price:"97.00", priceCurrency:"USD", availability:"https://schema.org/InStock" }
  };
  return (
    <main style={{padding:'3rem 1.5rem', maxWidth:880, margin:'0 auto'}}>
      <h1>Voice AI Agent</h1>
      <p>Book a free demo and see it in action.</p>
      <a href="/demo">Get Demo</a>
      <JsonLd data={ld} />
    </main>
  );
}
