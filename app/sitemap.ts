import { getAllSlugs } from "@/lib/posts";
export default async function sitemap() {
  const base = process.env.SITE_URL || "https://pro.cashinblue.com";
  const posts = (await getAllSlugs()).map((slug) => ({
    url: `${base}/blog/${slug}`, changefreq: "weekly" as const, priority: 0.7,
  }));
  return [
    { url: `${base}/`, changefreq: "weekly" as const, priority: 1.0 },
    { url: `${base}/demo`, changefreq: "weekly" as const, priority: 0.9 },
    ...posts,
  ];
}
