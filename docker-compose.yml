version: '3.8'
services:
  web:
    build: .
    container_name: cashinblue_web
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - SITE_NAME=${SITE_NAME}
      - SITE_URL=${SITE_URL}
      - SITE_DESC=${SITE_DESC}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - GHL_API_KEY=${GHL_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - VEO3_API_URL=${VEO3_API_URL}
      - VEO3_API_KEY=${VEO3_API_KEY}
      - CLICKBANK_AFFILIATE_ID=${CLICKBANK_AFFILIATE_ID}
    volumes:
      - ./.env:/app/.env:ro

  worker:
    build: .
    container_name: cashinblue_worker
    restart: unless-stopped
    depends_on:
      - web
    environment:
      - NODE_ENV=production
      - SITE_NAME=${SITE_NAME}
      - SITE_URL=${SITE_URL}
      - SITE_DESC=${SITE_DESC}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - GHL_API_KEY=${GHL_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - VEO3_API_URL=${VEO3_API_URL}
      - VEO3_API_KEY=${VEO3_API_KEY}
      - CLICKBANK_AFFILIATE_ID=${CLICKBANK_AFFILIATE_ID}
    command: ["npm","run","worker"]
    volumes:
      - ./.env:/app/.env:ro
