# Technical Guide — CashinBlue Next.js Stack

## Overview
This is a **React + Next.js (App Router)** application containerized with Docker, deployed on a VPS, and integrated with Supabase, Go High Level (GHL), OpenAI GPT-5, Google One Veo 3, and ClickBank.

The system replaces n8n workflows with **custom API routes and cron jobs** (node-cron) inside a `worker` service.

---

## Architecture
```
[Visitors] → [Next.js Frontend + API] → [Supabase DB]
                               ↔ GHL API
                               ↔ OpenAI API
                               ↔ Veo3 API
                               ↔ YouTube API
                               ↔ ClickBank Affiliate
```

### Services in docker-compose.yml
- **web**: Next.js app (marketing site, API routes, SEO)
- **worker**: Cron jobs for automation (daily YouTube Shorts, weekly blog posts)

### Key Directories
- `app/` — Next.js App Router pages, API endpoints, and metadata.
- `components/` — Reusable UI parts and JSON-LD helper.
- `lib/` — API clients for Supabase, GHL, OpenAI, Veo3, and post fetching.
- `scripts/` — Worker scripts (node-cron schedules).
- `supabase.sql` — Database schema for leads, videos, posts, events.

### Database (Supabase)
- `leads` — Captured leads from site forms.
- `videos` — YouTube Shorts script, captions, and publish status.
- `posts` — Blog content (SEO traffic).
- `events` — Log table for automation events/errors.

---

## API Routes
- `POST /api/lead` — Accepts JSON or form data; stores lead in Supabase; pushes to GHL.
- `GET /api/og` — Generates dynamic Open Graph images for SEO/social sharing.

---

## Worker Automation
The worker runs via `node-cron` and schedules:
- **Daily 09:00 ET**: Generate YouTube Shorts script (OpenAI), send to Veo3 for rendering, upload to YouTube (upload step placeholder — implement with YouTube API).
- **Weekly Monday 08:00 ET**: Placeholder for blog generation (extend with WordPress API or internal CMS).

---

## Deployment Steps
1. Fill in `.env` from `.env.example`.
2. Run `docker compose up --build -d`.
3. Configure DNS for your VPS to point to your domain.
4. Use a reverse proxy (Nginx/Traefik) to terminate SSL and forward to `web:3000`.

---

## Extending
- **YouTube Upload**: Implement OAuth or service account in worker.
- **Email Drip**: Add GHL API calls in `/api/lead` or worker.
- **Affiliate Integration**: Append ClickBank PS in blog posts/emails.
