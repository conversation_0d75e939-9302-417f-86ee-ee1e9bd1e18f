# User Guide — CashinBlue Next.js Stack

## Purpose
This stack is built to **automate your Voice AI Agent marketing**, capture leads, and generate content automatically — running on your VPS with no ongoing manual intervention.

---

## For First-Time Users

### 1. Prepare Environment Variables
Copy `.env.example` to `.env` and fill in:
- **SITE_NAME / SITE_URL / SITE_DESC**
- Supabase: `SUPABASE_URL`, `SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`
- Go High Level: `GHL_API_KEY`
- OpenAI: `OPENAI_API_KEY`
- Veo3: `VEO3_API_URL`, `VEO3_API_KEY`
- ClickBank: `CLICKBANK_AFFILIATE_ID`

### 2. Start the App
```bash
docker compose up --build -d
```
- **Web app**: Marketing site, landing pages, blog, SEO tags.
- **Worker**: Background jobs for YouTube Shorts and blog posts.

### 3. Check Pages
- `/` — Home page with CTA to demo
- `/demo` — Demo booking form
- `/services/voice-ai` — Product page with structured data
- `/blog/[slug]` — SEO blog pages

### 4. Lead Capture
When a user submits the `/demo` form:
1. Lead saved in Supabase.
2. (Optional) Sent to Go High Level (edit `/api/lead` to add workflow step).

### 5. Automation
- **Daily**: YouTube Shorts script & Veo3 job created automatically.
- **Weekly**: Blog post placeholder — extend as needed.

---

## Updating Content
- **Static pages**: Edit `app/*.tsx`.
- **Blog posts**: Insert into Supabase `posts` table or connect a CMS.
- **YouTube script topics**: Update `topics` array in `scripts/worker.ts`.

---

## SEO
- Titles, descriptions, canonical tags set per page in `metadata` exports.
- Open Graph images served from `/api/og`.

---

## Maintenance
- Keep `.env` updated if API keys change.
- Monitor logs:
```bash
docker compose logs -f web
docker compose logs -f worker
```
- Backup Supabase data regularly.
