const GHL_API = "https://rest.gohighlevel.com/v1";
const KEY = process.env.GHL_API_KEY || "";

export async function ghlFetch(path: string, init: RequestInit = {}) {
  if (!KEY) throw new Error("GHL_API_KEY missing");
  const res = await fetch(`${GHL_API}${path}`, {
    ...init,
    headers: {
      "Authorization": `Bearer ${KEY}`,
      "Content-Type": "application/json",
      ...(init.headers || {}),
    }
  });
  if (!res.ok) {
    const t = await res.text();
    throw new Error(`GHL ${path} failed: ${res.status} ${t}`);
  }
  return res.json();
}
