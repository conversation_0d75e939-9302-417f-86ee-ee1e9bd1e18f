export async function generateYouTubeScript(topic: string) {
  const key = process.env.OPENAI_API_KEY;
  if (!key) throw new Error("OPENAI_API_KEY missing");
  const prompt = `You are a DTC copywriter for SMB owners. Produce a 45–60s YouTube Shorts script.
GOAL: Book demos for a Voice AI Agent (inbound/outbound/web widget).
TOPIC: ${topic}
TONE: Clear, energetic, non-hype. Reading level: Grade 6–7.
OUTPUT (JSON):
{
 "title": "...",
 "script": "Line-by-line VO + on-screen text cues",
 "caption": "1–2 sentence description with CTA",
 "hashtags": "#voiceai #aibusiness #automation #smallbusiness"
}
Include: "Book a free AI demo at ${process.env.SITE_URL}/demo"`;

  const res = await fetch("https://api.openai.com/v1/chat/completions", {
    method: "POST",
    headers: {
      "Authorization": `Bearer ${key}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model: "gpt-5.1-mini", // placeholder; use your GPT-5 plan model
      messages: [
        { role: "system", content: "You write concise, high-converting scripts."},
        { role: "user", content: prompt }
      ],
      temperature: 0.7
    })
  });
  if (!res.ok) throw new Error("OpenAI API error");
  const data = await res.json();
  const content = data.choices?.[0]?.message?.content || "{}";
  return JSON.parse(content);
}
