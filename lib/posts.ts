import { supabase } from "./supabase";

export async function getPostBySlug(slug: string) {
  const { data, error } = await supabase
    .from("posts")
    .select("*")
    .eq("slug", slug)
    .single();
  if (error) return null;
  return {
    title: data.title,
    slug: data.slug,
    excerpt: data.excerpt,
    html: data.body_md, // assume HTML for simplicity
    publishedAt: data.created_at
  };
}

export async function getAllSlugs() {
  const { data } = await supabase.from("posts").select("slug");
  return (data || []).map((d:any) => d.slug);
}
