export async function createVeoJob(payload: any) {
  const url = process.env.VEO3_API_URL;
  const key = process.env.VEO3_API_KEY;
  if (!url || !key) throw new Error("VEO3 env missing");
  const res = await fetch(url + "/jobs", {
    method: "POST",
    headers: { "Authorization": `Bearer ${key}`, "Content-Type":"application/json" },
    body: JSON.stringify(payload)
  });
  if (!res.ok) throw new Error("Veo3 create job failed");
  return res.json();
}

export async function getVeoJob(id: string) {
  const url = process.env.VEO3_API_URL;
  const key = process.env.VEO3_API_KEY;
  const res = await fetch(`${url}/jobs/${id}`, {
    headers: { "Authorization": `Bearer ${key}` }
  });
  if (!res.ok) throw new Error("Veo3 get job failed");
  return res.json();
}
