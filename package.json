{"name": "cashinblue-next-stack", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3000", "worker": "node -r esbuild-register scripts/worker.ts"}, "dependencies": {"next": "14.2.5", "react": "18.3.1", "react-dom": "18.3.1", "@supabase/supabase-js": "2.45.4", "node-cron": "3.0.3", "zod": "3.23.8"}, "devDependencies": {"typescript": "5.5.4", "@types/node": "20.11.30", "@types/react": "18.2.66", "@types/react-dom": "18.2.22", "esbuild-register": "3.5.0"}}