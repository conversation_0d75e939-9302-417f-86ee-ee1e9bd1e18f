import cron from "node-cron";
import { supabaseService } from "@/lib/supabase";
import { generateYouTubeScript } from "@/lib/openai";
import { createVeoJob, getVeoJob } from "@/lib/veo3";

const topics = [
  "Inbound AI Agent answering missed calls",
  "Outbound AI Agent booking appointments",
  "Web Embed Widget converting website visitors",
  "AI vs Human rep: cost & performance",
  "Client success story (case study)",
  "FAQ: Does AI sound human?",
  "Limited-time trial offer"
];

async function dailyShort() {
  try {
    const topic = topics[Math.floor(Math.random()*topics.length)];
    const script = await generateYouTubeScript(topic);

    // Save ideation
    const { data: vid, error } = await supabaseService!
      .from("videos")
      .insert({ title: script.title, script: script.script, captions: script.caption, status: "IDEATED" })
      .select("*").single();
    if (error) throw error;

    // Create Veo job
    const job = await createVeoJob({
      script: script.script,
      shots: [{ type: "text", text: script.title }],
      aspect: "9:16",
      voice: "warm_pro",
      subtitles: true
    });

    // Poll (simple loop with backoff)
    let done = false;
    let mp4Url = "";
    for (let i=0;i<20 && !done;i++) {
      await new Promise(r=>setTimeout(r, 15000));
      const status = await getVeoJob(job.id);
      if (status?.state === "ready") {
        done = true;
        mp4Url = status.output?.mp4_url || "";
      }
    }

    await supabaseService!.from("videos")
      .update({ status: done ? "PUBLISHED":"FAILED", file_url: mp4Url })
      .eq("id", vid.id);

  } catch (e:any) {
    await supabaseService?.from("events").insert({ kind:"ERROR_VIDEO", payload:{ message: e.message }});
  }
}

// Weekly blog placeholder (extend with WordPress if needed)
async function weeklyBlog() {
  try {
    await supabaseService?.from("events").insert({ kind:"BLOG_TICK", payload:{} });
  } catch {}
}

// Schedule: daily at 09:00 and weekly Monday 08:00
cron.schedule("0 9 * * *", dailyShort, { timezone: "America/New_York" });
cron.schedule("0 8 * * 1", weeklyBlog, { timezone: "America/New_York" });

console.log("Worker started with dailyShort 09:00 ET and weeklyBlog 08:00 ET.");
