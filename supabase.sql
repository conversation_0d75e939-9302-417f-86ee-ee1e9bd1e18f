-- Supabase schema
create table if not exists leads(
  id uuid primary key default gen_random_uuid(),
  email text, phone text, name text,
  source text, utm jsonb, created_at timestamptz default now()
);

create table if not exists videos(
  id uuid primary key default gen_random_uuid(),
  title text, script text, captions text,
  file_url text, youtube_id text,
  status text check (status in ('IDEATED','RENDERING','PUBLISHED','FAILED')) default 'IDEATED',
  created_at timestamptz default now()
);

create table if not exists posts(
  id uuid primary key default gen_random_uuid(),
  title text, slug text, excerpt text, body_md text,
  wp_id int, status text, created_at timestamptz default now()
);

create table if not exists events(
  id bigserial primary key,
  kind text, payload jsonb, created_at timestamptz default now()
);
